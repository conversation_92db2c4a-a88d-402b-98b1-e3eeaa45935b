import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import threading
import os
import numpy as np
from forensic_fingerprint_processor import ForensicFingerprintProcessor, QualityMetrics
from bozorth_matcher import <PERSON><PERSON>thMatcher, MatchResult

class GovernmentGradeFingerprintApp:
    def __init__(self):
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("Government-Grade Fingerprint Analysis System")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Initialize processors
        self.processor = ForensicFingerprintProcessor()
        self.matcher = BozorthMatcher()
        
        # Variables
        self.image1_path = None
        self.image2_path = None
        self.fingerprint1_data = None
        self.fingerprint2_data = None
        self.comparison_result = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the professional user interface"""
        # Main container
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title with government styling
        title_frame = ctk.CTkFrame(main_frame)
        title_frame.pack(fill="x", pady=(0, 20))
        
        title_label = ctk.CTkLabel(
            title_frame, 
            text="🏛️ GOVERNMENT-GRADE FINGERPRINT ANALYSIS SYSTEM", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=15)
        
        subtitle_label = ctk.CTkLabel(
            title_frame, 
            text="FBI/NIST Standard • Forensic Quality • Statistical Validation", 
            font=ctk.CTkFont(size=14),
            text_color="gray70"
        )
        subtitle_label.pack(pady=(0, 10))
        
        # Content frame
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Configure grid
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=1)
        content_frame.grid_rowconfigure(1, weight=1)
        
        # Image upload sections
        self.setup_image_section(content_frame, "Subject Fingerprint", 0, 0, "image1")
        self.setup_image_section(content_frame, "Reference Fingerprint", 0, 1, "image2")
        
        # Analysis controls
        controls_frame = ctk.CTkFrame(content_frame)
        controls_frame.grid(row=2, column=0, columnspan=2, pady=20, padx=20, sticky="ew")
        
        # Analysis button
        self.analyze_btn = ctk.CTkButton(
            controls_frame,
            text="🔬 FORENSIC ANALYSIS",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            command=self.perform_analysis
        )
        self.analyze_btn.pack(pady=15)
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(controls_frame)
        self.progress_bar.pack(fill="x", padx=20, pady=(0, 15))
        self.progress_bar.set(0)
        
        # Status label
        self.status_label = ctk.CTkLabel(
            controls_frame,
            text="Ready for analysis",
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(pady=(0, 10))
        
        # Results section
        self.setup_results_section(content_frame)
        
    def setup_image_section(self, parent, title, row, col, image_type):
        """Setup professional image upload and analysis section"""
        # Frame for this image section
        img_frame = ctk.CTkFrame(parent)
        img_frame.grid(row=row, column=col, rowspan=2, padx=10, pady=10, sticky="nsew")
        img_frame.grid_rowconfigure(2, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(img_frame, text=title, font=ctk.CTkFont(size=18, weight="bold"))
        title_label.grid(row=0, column=0, pady=(15, 10))
        
        # Quality indicator
        quality_frame = ctk.CTkFrame(img_frame)
        quality_frame.grid(row=1, column=0, padx=15, pady=(0, 10), sticky="ew")
        
        quality_label = ctk.CTkLabel(quality_frame, text="Quality: Not Analyzed", font=ctk.CTkFont(size=12))
        quality_label.pack(pady=5)
        
        setattr(self, f"{image_type}_quality_label", quality_label)
        
        # Image display area
        img_display = ctk.CTkLabel(
            img_frame, 
            text="📁 Upload Fingerprint Image\n\nRequirements:\n• 500 DPI minimum\n• Clear ridge patterns\n• Minimal noise\n\nSupported: JPG, PNG, BMP",
            width=350,
            height=300,
            fg_color=("gray75", "gray25"),
            corner_radius=10,
            font=ctk.CTkFont(size=12)
        )
        img_display.grid(row=2, column=0, padx=15, pady=(0, 10), sticky="nsew")
        
        # Upload button
        upload_btn = ctk.CTkButton(
            img_frame,
            text="📁 Upload Image",
            command=lambda: self.upload_image(image_type, img_display)
        )
        upload_btn.grid(row=3, column=0, pady=(0, 15))
        
        # Store references
        setattr(self, f"{image_type}_display", img_display)
        setattr(self, f"{image_type}_upload_btn", upload_btn)
        
    def setup_results_section(self, parent):
        """Setup professional results display section"""
        # Results frame
        self.results_frame = ctk.CTkFrame(parent)
        self.results_frame.grid(row=4, column=0, columnspan=2, padx=20, pady=(0, 20), sticky="ew")
        
        # Results title
        results_title = ctk.CTkLabel(
            self.results_frame, 
            text="📊 FORENSIC ANALYSIS REPORT", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        results_title.pack(pady=(15, 10))
        
        # Results content frame
        self.results_content = ctk.CTkScrollableFrame(self.results_frame, height=300)
        self.results_content.pack(fill="both", expand=True, padx=15, pady=(0, 15))
        
        # Initially hidden
        self.results_frame.grid_remove()
        
    def upload_image(self, image_type, display_label):
        """Handle professional image upload with quality assessment"""
        file_types = [
            ("High-quality images", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title=f"Select {image_type.replace('image', 'Fingerprint ')}",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # Store path
                setattr(self, f"{image_type}_path", file_path)
                
                # Load and display image
                img = Image.open(file_path)
                
                # Resize for display
                img.thumbnail((320, 280), Image.Resampling.LANCZOS)
                
                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(img)
                
                # Update display
                display_label.configure(image=photo, text="")
                display_label.image = photo  # Keep a reference
                
                # Update button text
                btn = getattr(self, f"{image_type}_upload_btn")
                btn.configure(text="✅ Image Loaded")
                
                # Perform quality assessment
                self.assess_image_quality(image_type, file_path)
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load image: {str(e)}")
                
    def assess_image_quality(self, image_type, file_path):
        """Assess image quality using forensic standards"""
        try:
            # Process image for quality assessment
            result = self.processor.process_fingerprint(file_path)
            
            if result['status'] == 'success':
                quality = result['quality_metrics']
                quality_text = f"Quality: {quality.overall_quality:.1f}% "
                
                if quality.overall_quality >= 80:
                    quality_text += "🟢 EXCELLENT"
                    color = "green"
                elif quality.overall_quality >= 60:
                    quality_text += "🟡 GOOD"
                    color = "orange"
                elif quality.overall_quality >= 40:
                    quality_text += "🟠 FAIR"
                    color = "orange"
                else:
                    quality_text += "🔴 POOR"
                    color = "red"
                
                # Store processed data
                setattr(self, f"fingerprint{image_type[-1]}_data", result)
                
            elif result['status'] == 'rejected':
                quality_text = "🔴 REJECTED - Poor Quality"
                color = "red"
                setattr(self, f"fingerprint{image_type[-1]}_data", None)
            else:
                quality_text = "❌ PROCESSING ERROR"
                color = "red"
                setattr(self, f"fingerprint{image_type[-1]}_data", None)
            
            # Update quality label
            quality_label = getattr(self, f"{image_type}_quality_label")
            quality_label.configure(text=quality_text, text_color=color)
            
        except Exception as e:
            quality_label = getattr(self, f"{image_type}_quality_label")
            quality_label.configure(text="❌ Quality Assessment Failed", text_color="red")
            
    def perform_analysis(self):
        """Perform comprehensive forensic analysis"""
        if not self.image1_path or not self.image2_path:
            messagebox.showwarning("Warning", "Please upload both fingerprint images first!")
            return
            
        if not self.fingerprint1_data or not self.fingerprint2_data:
            messagebox.showerror("Error", "One or both images failed quality assessment!")
            return
            
        # Disable button and show progress
        self.analyze_btn.configure(state="disabled", text="🔄 ANALYZING...")
        self.progress_bar.set(0)
        self.results_frame.grid_remove()
        self.status_label.configure(text="Initializing forensic analysis...")
        
        # Run analysis in separate thread
        thread = threading.Thread(target=self.run_forensic_analysis)
        thread.daemon = True
        thread.start()
        
    def run_forensic_analysis(self):
        """Run comprehensive forensic analysis in background thread"""
        try:
            # Update progress
            self.root.after(0, lambda: self.progress_bar.set(0.2))
            self.root.after(0, lambda: self.status_label.configure(text="Extracting minutiae features..."))
            
            # Extract minutiae from both fingerprints
            minutiae1 = self.fingerprint1_data['minutiae']
            minutiae2 = self.fingerprint2_data['minutiae']
            
            self.root.after(0, lambda: self.progress_bar.set(0.5))
            self.root.after(0, lambda: self.status_label.configure(text="Performing Bozorth3 matching..."))
            
            # Perform professional matching
            match_result = self.matcher.match_fingerprints(minutiae1, minutiae2)
            
            self.root.after(0, lambda: self.progress_bar.set(0.8))
            self.root.after(0, lambda: self.status_label.configure(text="Generating forensic report..."))
            
            # Store result
            self.comparison_result = {
                'match_result': match_result,
                'fingerprint1_data': self.fingerprint1_data,
                'fingerprint2_data': self.fingerprint2_data,
                'classification': self.matcher.get_match_classification(match_result)
            }
            
            # Update UI in main thread
            self.root.after(0, self.display_forensic_results)
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Analysis failed: {str(e)}"))
            self.root.after(0, self.reset_analyze_button)
            
    def display_forensic_results(self):
        """Display comprehensive forensic analysis results"""
        if not self.comparison_result:
            self.reset_analyze_button()
            return
            
        # Clear previous results
        for widget in self.results_content.winfo_children():
            widget.destroy()
            
        result = self.comparison_result['match_result']
        classification = self.comparison_result['classification']
        
        # Classification header
        classification_frame = ctk.CTkFrame(self.results_content)
        classification_frame.pack(fill="x", pady=(0, 20))
        
        # Determine color based on classification
        if classification == "IDENTIFICATION":
            color = "green"
            icon = "✅"
        elif classification == "PROBABLE_MATCH":
            color = "orange"
            icon = "⚠️"
        elif classification == "POSSIBLE_MATCH":
            color = "yellow"
            icon = "🔍"
        elif classification == "INCONCLUSIVE":
            color = "gray"
            icon = "❓"
        else:  # EXCLUSION
            color = "red"
            icon = "❌"
            
        # Classification display
        ctk.CTkLabel(
            classification_frame,
            text=f"{icon} {classification}",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color=color
        ).pack(pady=10)
        
        ctk.CTkLabel(
            classification_frame,
            text=f"Match Score: {result.match_score:.1f}%",
            font=ctk.CTkFont(size=18),
            text_color=color
        ).pack()
        
        # Statistical Analysis
        stats_frame = ctk.CTkFrame(self.results_content)
        stats_frame.pack(fill="x", pady=(0, 20))
        
        ctk.CTkLabel(
            stats_frame,
            text="📈 STATISTICAL ANALYSIS",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        stats_text = f"""
Confidence Level: {result.confidence_level:.1f}%
Statistical Significance: {result.statistical_significance:.1f}%
Match Probability: {result.match_probability:.3f}
False Match Rate: {result.false_match_probability:.2e}
        """
        
        ctk.CTkLabel(
            stats_frame,
            text=stats_text,
            font=ctk.CTkFont(size=12),
            justify="left"
        ).pack(pady=(0, 10))
        
        # Minutiae Analysis
        minutiae_frame = ctk.CTkFrame(self.results_content)
        minutiae_frame.pack(fill="x", pady=(0, 20))
        
        ctk.CTkLabel(
            minutiae_frame,
            text="🔬 MINUTIAE ANALYSIS",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        minutiae_text = f"""
Matched Minutiae: {result.matched_minutiae_count}
Subject Minutiae Count: {result.total_minutiae_1}
Reference Minutiae Count: {result.total_minutiae_2}
Match Efficiency: {(result.matched_minutiae_count / min(result.total_minutiae_1, result.total_minutiae_2) * 100):.1f}%
        """
        
        ctk.CTkLabel(
            minutiae_frame,
            text=minutiae_text,
            font=ctk.CTkFont(size=12),
            justify="left"
        ).pack(pady=(0, 10))
        
        # Quality Metrics
        quality_frame = ctk.CTkFrame(self.results_content)
        quality_frame.pack(fill="x", pady=(0, 20))
        
        ctk.CTkLabel(
            quality_frame,
            text="🏆 QUALITY ASSESSMENT",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        q1 = self.comparison_result['fingerprint1_data']['quality_metrics']
        q2 = self.comparison_result['fingerprint2_data']['quality_metrics']
        
        quality_text = f"""
Subject Image Quality: {q1.overall_quality:.1f}% (Clarity: {q1.clarity:.1f}%, Contrast: {q1.contrast:.1f}%)
Reference Image Quality: {q2.overall_quality:.1f}% (Clarity: {q2.clarity:.1f}%, Contrast: {q2.contrast:.1f}%)
Ridge Flow Quality: Subject {q1.ridge_flow_quality:.1f}%, Reference {q2.ridge_flow_quality:.1f}%
        """
        
        ctk.CTkLabel(
            quality_frame,
            text=quality_text,
            font=ctk.CTkFont(size=12),
            justify="left"
        ).pack(pady=(0, 10))
        
        # Forensic Conclusion
        conclusion_frame = ctk.CTkFrame(self.results_content)
        conclusion_frame.pack(fill="x", pady=(0, 10))
        
        ctk.CTkLabel(
            conclusion_frame,
            text="⚖️ FORENSIC CONCLUSION",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        if classification == "IDENTIFICATION":
            conclusion = "The fingerprints are from the SAME individual with high confidence."
        elif classification == "PROBABLE_MATCH":
            conclusion = "The fingerprints PROBABLY belong to the same individual. Manual verification recommended."
        elif classification == "POSSIBLE_MATCH":
            conclusion = "The fingerprints POSSIBLY belong to the same individual. Further analysis required."
        elif classification == "INCONCLUSIVE":
            conclusion = "The analysis is INCONCLUSIVE. Additional evidence required."
        else:
            conclusion = "The fingerprints are from DIFFERENT individuals."
        
        ctk.CTkLabel(
            conclusion_frame,
            text=conclusion,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=color,
            wraplength=600
        ).pack(pady=(0, 10))
        
        # Show results and complete progress
        self.progress_bar.set(1.0)
        self.status_label.configure(text="Forensic analysis complete")
        self.results_frame.grid()
        self.reset_analyze_button()
        
    def reset_analyze_button(self):
        """Reset analyze button to original state"""
        self.analyze_btn.configure(state="normal", text="🔬 FORENSIC ANALYSIS")
        
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = GovernmentGradeFingerprintApp()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")
