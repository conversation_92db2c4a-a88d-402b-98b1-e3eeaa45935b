#!/usr/bin/env python3
"""
Test script for fingerprint comparison functionality
"""

import sys
import os
from fingerprint_processor import FingerprintProcessor

def test_processor():
    """Test the fingerprint processor with basic functionality"""
    print("🧪 Testing Fingerprint Processor...")
    
    try:
        # Initialize processor
        processor = FingerprintProcessor()
        print("✅ Processor initialized successfully")
        
        # Test image preprocessing (with dummy data)
        import numpy as np
        import cv2
        
        # Create a test image
        test_img = np.random.randint(0, 255, (200, 200), dtype=np.uint8)
        cv2.imwrite("test_image.jpg", test_img)
        
        # Test preprocessing
        processed = processor.preprocess_image("test_image.jpg")
        print(f"✅ Image preprocessing works - Output shape: {processed.shape}")
        
        # Test minutiae extraction
        minutiae, skeleton = processor.extract_minutiae(processed)
        print(f"✅ Minutiae extraction works - Found {len(minutiae)} minutiae points")
        
        # Test SIFT features
        kp, desc = processor.extract_sift_features(processed)
        print(f"✅ SIFT feature extraction works - Found {len(kp) if kp else 0} keypoints")
        
        # Clean up
        os.remove("test_image.jpg")
        
        print("🎉 All tests passed! The processor is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def check_dependencies():
    """Check if all required dependencies are available"""
    print("📦 Checking dependencies...")
    
    required_modules = [
        'cv2', 'numpy', 'PIL', 'skimage', 'matplotlib', 'customtkinter'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - MISSING")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n⚠️ Missing modules: {', '.join(missing_modules)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    else:
        print("\n🎉 All dependencies are available!")
        return True

def main():
    """Main test function"""
    print("🔍 Fingerprint Comparison Tool - Test Suite")
    print("=" * 50)
    
    # Check dependencies first
    if not check_dependencies():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # Test processor functionality
    if not test_processor():
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🚀 Ready to run the main application!")
    print("Run: python main.py")

if __name__ == "__main__":
    main()
