import cv2
import numpy as np
from skimage import morphology, filters
from skimage.feature import peak_local_maxima
import matplotlib.pyplot as plt

class FingerprintProcessor:
    def __init__(self):
        self.sift = cv2.SIFT_create()
        self.orb = cv2.ORB_create()
        
    def preprocess_image(self, image_path):
        """Preprocess fingerprint image for better feature extraction"""
        # Read image
        img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        # Resize if too large
        height, width = img.shape
        if width > 800 or height > 800:
            scale = min(800/width, 800/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = cv2.resize(img, (new_width, new_height))
        
        # Normalize
        img = cv2.equalizeHist(img)
        
        # Gaussian blur to reduce noise
        img = cv2.GaussianBlur(img, (3, 3), 0)
        
        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        img = clahe.apply(img)
        
        return img
    
    def extract_minutiae(self, img):
        """Extract minutiae points from fingerprint"""
        # Apply Gabor filter for ridge enhancement
        enhanced = self.apply_gabor_filter(img)
        
        # Binarize
        _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Skeletonize
        skeleton = morphology.skeletonize(binary // 255)
        skeleton = (skeleton * 255).astype(np.uint8)
        
        # Find minutiae points
        minutiae = self.find_minutiae_points(skeleton)
        
        return minutiae, skeleton
    
    def apply_gabor_filter(self, img):
        """Apply Gabor filter for ridge enhancement"""
        # Create Gabor kernels with different orientations
        kernels = []
        for theta in range(0, 180, 22):  # 8 orientations
            kernel = cv2.getGaborKernel((21, 21), 5, np.radians(theta), 2*np.pi*0.1, 0.5, 0, ktype=cv2.CV_32F)
            kernels.append(kernel)
        
        # Apply filters and take maximum response
        filtered_imgs = []
        for kernel in kernels:
            filtered = cv2.filter2D(img, cv2.CV_8UC3, kernel)
            filtered_imgs.append(filtered)
        
        # Combine responses
        enhanced = np.maximum.reduce(filtered_imgs)
        return enhanced
    
    def find_minutiae_points(self, skeleton):
        """Find minutiae points (ridge endings and bifurcations)"""
        minutiae = []
        
        # Define 3x3 neighborhood patterns for minutiae detection
        # Ridge ending: has only one neighbor
        # Bifurcation: has three or more neighbors
        
        for i in range(1, skeleton.shape[0] - 1):
            for j in range(1, skeleton.shape[1] - 1):
                if skeleton[i, j] == 255:  # Ridge pixel
                    # Count neighbors
                    neighbors = 0
                    for di in [-1, 0, 1]:
                        for dj in [-1, 0, 1]:
                            if di == 0 and dj == 0:
                                continue
                            if skeleton[i + di, j + dj] == 255:
                                neighbors += 1
                    
                    # Ridge ending (1 neighbor) or bifurcation (3+ neighbors)
                    if neighbors == 1 or neighbors >= 3:
                        minutiae.append((j, i, neighbors))  # (x, y, type)
        
        return minutiae
    
    def extract_sift_features(self, img):
        """Extract SIFT features for additional matching"""
        keypoints, descriptors = self.sift.detectAndCompute(img, None)
        return keypoints, descriptors
    
    def extract_orb_features(self, img):
        """Extract ORB features as backup"""
        keypoints, descriptors = self.orb.detectAndCompute(img, None)
        return keypoints, descriptors
    
    def compare_fingerprints(self, img1_path, img2_path):
        """Compare two fingerprint images and return similarity score"""
        try:
            # Preprocess images
            img1 = self.preprocess_image(img1_path)
            img2 = self.preprocess_image(img2_path)
            
            # Extract minutiae
            minutiae1, skeleton1 = self.extract_minutiae(img1)
            minutiae2, skeleton2 = self.extract_minutiae(img2)
            
            # Extract SIFT features
            kp1, desc1 = self.extract_sift_features(img1)
            kp2, desc2 = self.extract_sift_features(img2)
            
            # Calculate similarity scores
            minutiae_score = self.compare_minutiae(minutiae1, minutiae2)
            sift_score = self.compare_sift_features(desc1, desc2)
            
            # More conservative combination with stricter thresholds
            # Both scores must be reasonable for a positive match
            if minutiae_score < 20 and sift_score < 20:
                final_score = 0.0
            elif minutiae_score < 10 or sift_score < 10:
                final_score = min(minutiae_score, sift_score) * 0.5
            else:
                # Weighted average with higher weight on minutiae
                final_score = (minutiae_score * 0.7 + sift_score * 0.3)

            # Apply final threshold to reduce false positives
            if final_score < 25:
                final_score = 0.0
            
            return {
                'similarity_percentage': final_score,
                'minutiae_score': minutiae_score,
                'sift_score': sift_score,
                'minutiae_count_1': len(minutiae1),
                'minutiae_count_2': len(minutiae2),
                'sift_features_1': len(kp1) if kp1 else 0,
                'sift_features_2': len(kp2) if kp2 else 0,
                'processed_img1': img1,
                'processed_img2': img2,
                'minutiae1': minutiae1,
                'minutiae2': minutiae2
            }
            
        except Exception as e:
            return {'error': str(e), 'similarity_percentage': 0}
    
    def compare_minutiae(self, minutiae1, minutiae2):
        """Compare minutiae points between two fingerprints with improved accuracy"""
        if not minutiae1 or not minutiae2:
            return 0.0

        # More strict tolerance for better accuracy
        tolerance = 10  # pixels - reduced from 15
        matches = 0
        used_minutiae2 = set()  # Track used minutiae to avoid double matching

        for m1 in minutiae1:
            best_match = None
            best_distance = float('inf')

            for i, m2 in enumerate(minutiae2):
                if i in used_minutiae2:
                    continue

                # Calculate distance
                dist = np.sqrt((m1[0] - m2[0])**2 + (m1[1] - m2[1])**2)

                # Must be same type and within tolerance
                if dist < tolerance and m1[2] == m2[2] and dist < best_distance:
                    best_match = i
                    best_distance = dist

            if best_match is not None:
                matches += 1
                used_minutiae2.add(best_match)

        # More conservative scoring
        min_minutiae = min(len(minutiae1), len(minutiae2))
        max_minutiae = max(len(minutiae1), len(minutiae2))

        if min_minutiae == 0:
            return 0.0

        # Penalize large differences in minutiae count
        count_ratio = min_minutiae / max_minutiae
        if count_ratio < 0.5:  # Very different minutiae counts
            return 0.0

        # Calculate similarity with stricter requirements
        match_ratio = matches / min_minutiae
        similarity = match_ratio * count_ratio * 100

        # Require minimum number of matches for any positive score
        if matches < 3:  # Need at least 3 matching minutiae
            similarity = 0.0

        return min(similarity, 100.0)
    
    def compare_sift_features(self, desc1, desc2):
        """Compare SIFT descriptors with improved accuracy"""
        if desc1 is None or desc2 is None or len(desc1) == 0 or len(desc2) == 0:
            return 0.0

        # More strict ratio test for better accuracy
        ratio_threshold = 0.6  # Reduced from 0.7 for stricter matching

        try:
            # Use FLANN matcher for better performance
            FLANN_INDEX_KDTREE = 1
            index_params = dict(algorithm=FLANN_INDEX_KDTREE, trees=5)
            search_params = dict(checks=50)

            flann = cv2.FlannBasedMatcher(index_params, search_params)
            matches = flann.knnMatch(desc1, desc2, k=2)

            # Apply stricter Lowe's ratio test
            good_matches = []
            for match_pair in matches:
                if len(match_pair) == 2:
                    m, n = match_pair
                    if m.distance < ratio_threshold * n.distance:
                        good_matches.append(m)

            # More conservative scoring
            min_features = min(len(desc1), len(desc2))
            max_features = max(len(desc1), len(desc2))

            if min_features == 0:
                return 0.0

            # Penalize large differences in feature count
            feature_ratio = min_features / max_features
            if feature_ratio < 0.3:  # Very different feature counts
                return 0.0

            # Calculate similarity with stricter requirements
            match_ratio = len(good_matches) / min_features
            similarity = match_ratio * feature_ratio * 100

            # Require minimum number of matches
            if len(good_matches) < 5:  # Need at least 5 good matches
                similarity = 0.0

            return min(similarity, 100.0)

        except Exception:
            # Fallback to brute force matcher with same strict criteria
            try:
                bf = cv2.BFMatcher()
                matches = bf.knnMatch(desc1, desc2, k=2)

                good_matches = []
                for match_pair in matches:
                    if len(match_pair) == 2:
                        m, n = match_pair
                        if m.distance < ratio_threshold * n.distance:
                            good_matches.append(m)

                min_features = min(len(desc1), len(desc2))
                max_features = max(len(desc1), len(desc2))

                if min_features == 0:
                    return 0.0

                feature_ratio = min_features / max_features
                if feature_ratio < 0.3:
                    return 0.0

                match_ratio = len(good_matches) / min_features
                similarity = match_ratio * feature_ratio * 100

                if len(good_matches) < 5:
                    similarity = 0.0

                return min(similarity, 100.0)

            except Exception:
                return 0.0
