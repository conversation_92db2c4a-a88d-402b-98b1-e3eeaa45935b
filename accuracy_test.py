#!/usr/bin/env python3
"""
Accuracy test script for fingerprint comparison
Tests the algorithm with known same/different fingerprint pairs
"""

import os
import sys
import numpy as np
from PIL import Image
import time

def create_test_images():
    """Create test fingerprint-like images for testing"""
    print("🧪 Creating test images...")
    
    # Create test directory
    if not os.path.exists("test_images"):
        os.makedirs("test_images")
    
    # Create similar fingerprint patterns
    def create_fingerprint_pattern(seed, variation=0):
        np.random.seed(seed)
        size = 200
        img = np.zeros((size, size), dtype=np.uint8)
        
        # Create ridge-like patterns
        for i in range(size):
            for j in range(size):
                # Create sinusoidal ridge pattern
                angle = np.pi/4 + variation * 0.1
                freq = 0.1 + variation * 0.01
                ridge_value = np.sin(i * freq * np.cos(angle) + j * freq * np.sin(angle))
                
                # Add some noise
                noise = np.random.normal(0, 0.1)
                value = (ridge_value + noise + 1) * 127.5
                img[i, j] = np.clip(value, 0, 255)
        
        # Add some random minutiae-like features
        for _ in range(10 + variation):
            x, y = np.random.randint(20, size-20, 2)
            radius = np.random.randint(3, 8)
            for di in range(-radius, radius+1):
                for dj in range(-radius, radius+1):
                    if 0 <= x+di < size and 0 <= y+dj < size:
                        if di*di + dj*dj <= radius*radius:
                            img[x+di, y+dj] = np.random.randint(0, 255)
        
        return img
    
    # Create test image pairs
    test_cases = [
        # Same person (should match)
        ("same_person_1a.jpg", "same_person_1b.jpg", True, 42, 0),
        ("same_person_2a.jpg", "same_person_2b.jpg", True, 123, 1),
        ("same_person_3a.jpg", "same_person_3b.jpg", True, 456, 2),
        
        # Different people (should NOT match)
        ("different_1.jpg", "different_2.jpg", False, 789, 999),
        ("different_3.jpg", "different_4.jpg", False, 111, 222),
        ("different_5.jpg", "different_6.jpg", False, 333, 444),
    ]
    
    for img1_name, img2_name, should_match, seed1, seed2 in test_cases:
        img1 = create_fingerprint_pattern(seed1, 0 if should_match else seed2)
        img2 = create_fingerprint_pattern(seed1, 2 if should_match else seed2)
        
        Image.fromarray(img1).save(f"test_images/{img1_name}")
        Image.fromarray(img2).save(f"test_images/{img2_name}")
        
        print(f"✅ Created {img1_name} and {img2_name} ({'SAME' if should_match else 'DIFFERENT'} person)")
    
    return test_cases

def test_simple_algorithm():
    """Test the simple algorithm"""
    print("\n🔍 Testing Simple Algorithm...")
    print("=" * 50)
    
    try:
        from simple_fingerprint_gui import SimpleFingerprintApp
        app = SimpleFingerprintApp()
        
        test_cases = create_test_images()
        correct_predictions = 0
        total_tests = len(test_cases)
        
        for img1_name, img2_name, should_match, _, _ in test_cases:
            img1_path = f"test_images/{img1_name}"
            img2_path = f"test_images/{img2_name}"
            
            # Set paths
            app.image1_path = img1_path
            app.image2_path = img2_path
            
            # Run comparison
            result = app.simple_compare()
            similarity = result.get('similarity_percentage', 0)
            
            # Determine prediction (using 50% threshold)
            predicted_match = similarity >= 50
            
            # Check if correct
            is_correct = predicted_match == should_match
            if is_correct:
                correct_predictions += 1
            
            status = "✅ CORRECT" if is_correct else "❌ WRONG"
            expected = "MATCH" if should_match else "NO MATCH"
            predicted = "MATCH" if predicted_match else "NO MATCH"
            
            print(f"{status} | {img1_name} vs {img2_name}")
            print(f"   Expected: {expected} | Predicted: {predicted} | Similarity: {similarity:.1f}%")
            print()
        
        accuracy = (correct_predictions / total_tests) * 100
        print(f"📊 Simple Algorithm Accuracy: {accuracy:.1f}% ({correct_predictions}/{total_tests})")
        
        return accuracy
        
    except Exception as e:
        print(f"❌ Error testing simple algorithm: {e}")
        return 0

def test_advanced_algorithm():
    """Test the advanced algorithm"""
    print("\n🔬 Testing Advanced Algorithm...")
    print("=" * 50)
    
    try:
        from fingerprint_processor import FingerprintProcessor
        processor = FingerprintProcessor()
        
        test_cases = create_test_images()
        correct_predictions = 0
        total_tests = len(test_cases)
        
        for img1_name, img2_name, should_match, _, _ in test_cases:
            img1_path = f"test_images/{img1_name}"
            img2_path = f"test_images/{img2_name}"
            
            # Run comparison
            result = processor.compare_fingerprints(img1_path, img2_path)
            
            if 'error' in result:
                print(f"❌ Error comparing {img1_name} vs {img2_name}: {result['error']}")
                continue
            
            similarity = result.get('similarity_percentage', 0)
            
            # Determine prediction (using 50% threshold)
            predicted_match = similarity >= 50
            
            # Check if correct
            is_correct = predicted_match == should_match
            if is_correct:
                correct_predictions += 1
            
            status = "✅ CORRECT" if is_correct else "❌ WRONG"
            expected = "MATCH" if should_match else "NO MATCH"
            predicted = "MATCH" if predicted_match else "NO MATCH"
            
            print(f"{status} | {img1_name} vs {img2_name}")
            print(f"   Expected: {expected} | Predicted: {predicted} | Similarity: {similarity:.1f}%")
            print(f"   Minutiae: {result.get('minutiae_score', 0):.1f}% | SIFT: {result.get('sift_score', 0):.1f}%")
            print()
        
        accuracy = (correct_predictions / total_tests) * 100
        print(f"📊 Advanced Algorithm Accuracy: {accuracy:.1f}% ({correct_predictions}/{total_tests})")
        
        return accuracy
        
    except Exception as e:
        print(f"❌ Error testing advanced algorithm: {e}")
        return 0

def main():
    """Main test function"""
    print("🧪 Fingerprint Algorithm Accuracy Test")
    print("=" * 60)
    print("This test creates synthetic fingerprint-like images and")
    print("tests the accuracy of both simple and advanced algorithms.")
    print("=" * 60)
    
    # Test both algorithms
    simple_accuracy = test_simple_algorithm()
    advanced_accuracy = test_advanced_algorithm()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    print(f"Simple Algorithm Accuracy:   {simple_accuracy:.1f}%")
    print(f"Advanced Algorithm Accuracy: {advanced_accuracy:.1f}%")
    
    if simple_accuracy >= 80:
        print("✅ Simple algorithm shows good accuracy!")
    elif simple_accuracy >= 60:
        print("⚠️ Simple algorithm shows moderate accuracy")
    else:
        print("❌ Simple algorithm needs improvement")
    
    if advanced_accuracy >= 80:
        print("✅ Advanced algorithm shows good accuracy!")
    elif advanced_accuracy >= 60:
        print("⚠️ Advanced algorithm shows moderate accuracy")
    else:
        print("❌ Advanced algorithm needs improvement")
    
    print("\n💡 Tips for better accuracy:")
    print("- Use high-quality fingerprint images (300+ DPI)")
    print("- Ensure good contrast and minimal noise")
    print("- Clean fingerprint scanner regularly")
    print("- Test with real fingerprint images for validation")
    
    # Cleanup
    try:
        import shutil
        if os.path.exists("test_images"):
            shutil.rmtree("test_images")
        print("\n🧹 Cleaned up test images")
    except:
        pass

if __name__ == "__main__":
    main()
