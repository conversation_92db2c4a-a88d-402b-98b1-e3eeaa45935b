import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
import os
import numpy as np

class SimpleFingerprintApp:
    def __init__(self):
        # Create main window
        self.root = tk.Tk()
        self.root.title("Fingerprint Comparison Tool")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2b2b2b')
        
        # Variables
        self.image1_path = None
        self.image2_path = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 24, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Heading.TLabel', font=('Arial', 14, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Custom.TButton', font=('Arial', 12, 'bold'))
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 Fingerprint Comparison Tool", style='Title.TLabel')
        title_label.pack(pady=(0, 30))
        
        # Content frame
        content_frame = tk.Frame(main_frame, bg='#2b2b2b')
        content_frame.pack(fill="both", expand=True)
        
        # Image sections
        images_frame = tk.Frame(content_frame, bg='#2b2b2b')
        images_frame.pack(fill="x", pady=20)
        
        # Image 1 section
        img1_frame = tk.Frame(images_frame, bg='#3b3b3b', relief='raised', bd=2)
        img1_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        ttk.Label(img1_frame, text="Fingerprint 1", style='Heading.TLabel').pack(pady=10)
        
        self.img1_display = tk.Label(
            img1_frame, 
            text="📁 Click to upload image\n\nSupported formats:\nJPG, PNG, BMP",
            width=40, height=15,
            bg='#4b4b4b', fg='white',
            font=('Arial', 10)
        )
        self.img1_display.pack(padx=15, pady=10)
        
        self.upload1_btn = ttk.Button(
            img1_frame, 
            text="📁 Upload Image 1",
            command=lambda: self.upload_image("image1", self.img1_display),
            style='Custom.TButton'
        )
        self.upload1_btn.pack(pady=(0, 15))
        
        # Image 2 section
        img2_frame = tk.Frame(images_frame, bg='#3b3b3b', relief='raised', bd=2)
        img2_frame.pack(side="right", fill="both", expand=True, padx=(10, 0))
        
        ttk.Label(img2_frame, text="Fingerprint 2", style='Heading.TLabel').pack(pady=10)
        
        self.img2_display = tk.Label(
            img2_frame, 
            text="📁 Click to upload image\n\nSupported formats:\nJPG, PNG, BMP",
            width=40, height=15,
            bg='#4b4b4b', fg='white',
            font=('Arial', 10)
        )
        self.img2_display.pack(padx=15, pady=10)
        
        self.upload2_btn = ttk.Button(
            img2_frame, 
            text="📁 Upload Image 2",
            command=lambda: self.upload_image("image2", self.img2_display),
            style='Custom.TButton'
        )
        self.upload2_btn.pack(pady=(0, 15))
        
        # Compare button
        self.compare_btn = ttk.Button(
            content_frame,
            text="🔍 Compare Fingerprints",
            command=self.compare_fingerprints,
            style='Custom.TButton'
        )
        self.compare_btn.pack(pady=20)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            content_frame, 
            variable=self.progress_var, 
            maximum=100
        )
        self.progress_bar.pack(fill="x", padx=50, pady=10)
        
        # Results frame
        self.results_frame = tk.Frame(content_frame, bg='#3b3b3b', relief='raised', bd=2)
        self.results_frame.pack(fill="x", pady=20)
        self.results_frame.pack_forget()  # Initially hidden
        
    def upload_image(self, image_type, display_label):
        """Handle image upload"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("All files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title=f"Select {image_type.replace('image', 'Fingerprint ')}",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # Store path
                setattr(self, f"{image_type}_path", file_path)
                
                # Load and display image
                img = Image.open(file_path)
                
                # Resize for display
                img.thumbnail((300, 200), Image.Resampling.LANCZOS)
                
                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(img)
                
                # Update display
                display_label.configure(image=photo, text="")
                display_label.image = photo  # Keep a reference
                
                # Update button text
                if image_type == "image1":
                    self.upload1_btn.configure(text="✅ Image 1 Loaded")
                else:
                    self.upload2_btn.configure(text="✅ Image 2 Loaded")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load image: {str(e)}")
                
    def compare_fingerprints(self):
        """Compare the two uploaded fingerprints"""
        if not self.image1_path or not self.image2_path:
            messagebox.showwarning("Warning", "Please upload both fingerprint images first!")
            return
            
        # Disable button and show progress
        self.compare_btn.configure(state="disabled", text="🔄 Comparing...")
        self.progress_var.set(0)
        self.results_frame.pack_forget()
        
        # Run comparison in separate thread
        thread = threading.Thread(target=self.run_comparison)
        thread.daemon = True
        thread.start()
        
    def run_comparison(self):
        """Run fingerprint comparison in background thread"""
        try:
            # Simulate processing steps
            for i in range(1, 6):
                self.root.after(0, lambda p=i*20: self.progress_var.set(p))
                threading.Event().wait(0.5)  # Simulate processing time
            
            # Simple comparison based on image properties
            result = self.simple_compare()
            
            # Update UI in main thread
            self.root.after(0, lambda: self.display_results(result))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Comparison failed: {str(e)}"))
            self.root.after(0, self.reset_compare_button)
            
    def simple_compare(self):
        """Simple comparison based on image properties"""
        try:
            # Load images
            img1 = Image.open(self.image1_path).convert('L')
            img2 = Image.open(self.image2_path).convert('L')
            
            # Resize to same size for comparison
            size = (200, 200)
            img1 = img1.resize(size)
            img2 = img2.resize(size)
            
            # Convert to numpy arrays
            arr1 = np.array(img1)
            arr2 = np.array(img2)
            
            # Calculate similarity using correlation coefficient
            correlation = np.corrcoef(arr1.flatten(), arr2.flatten())[0, 1]
            
            # Convert to percentage (handle NaN)
            if np.isnan(correlation):
                similarity = 0.0
            else:
                similarity = max(0, correlation * 100)
            
            # Calculate additional metrics
            mse = np.mean((arr1 - arr2) ** 2)
            max_mse = 255 ** 2
            structural_similarity = max(0, (1 - mse / max_mse) * 100)
            
            # Combine metrics
            final_similarity = (similarity * 0.7 + structural_similarity * 0.3)
            
            return {
                'similarity_percentage': final_similarity,
                'correlation': correlation,
                'structural_similarity': structural_similarity,
                'mse': mse
            }
            
        except Exception as e:
            return {'error': str(e), 'similarity_percentage': 0}
    
    def display_results(self, result):
        """Display comparison results"""
        if 'error' in result:
            messagebox.showerror("Error", result['error'])
            self.reset_compare_button()
            return
            
        # Clear previous results
        for widget in self.results_frame.winfo_children():
            widget.destroy()
            
        similarity = result['similarity_percentage']
        
        # Results title
        ttk.Label(
            self.results_frame, 
            text="📊 Comparison Results", 
            style='Heading.TLabel'
        ).pack(pady=15)
        
        # Main similarity score
        similarity_frame = tk.Frame(self.results_frame, bg='#3b3b3b')
        similarity_frame.pack(pady=10)
        
        # Determine color and status based on similarity
        if similarity >= 80:
            color = "green"
            status = "✅ STRONG MATCH"
        elif similarity >= 60:
            color = "orange"
            status = "⚠️ PARTIAL MATCH"
        elif similarity >= 40:
            color = "yellow"
            status = "🔍 WEAK MATCH"
        else:
            color = "red"
            status = "❌ NO MATCH"
            
        # Similarity percentage
        tk.Label(
            similarity_frame,
            text=f"{similarity:.1f}%",
            font=('Arial', 32, 'bold'),
            fg=color,
            bg='#3b3b3b'
        ).pack()
        
        # Status
        tk.Label(
            similarity_frame,
            text=status,
            font=('Arial', 16, 'bold'),
            fg=color,
            bg='#3b3b3b'
        ).pack()
        
        # Detailed metrics
        details_frame = tk.Frame(self.results_frame, bg='#4b4b4b')
        details_frame.pack(fill="x", padx=20, pady=10)
        
        tk.Label(
            details_frame,
            text="📈 Detailed Analysis",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='#4b4b4b'
        ).pack(pady=5)
        
        metrics_text = f"""
Correlation Coefficient: {result.get('correlation', 0):.3f}
Structural Similarity: {result.get('structural_similarity', 0):.1f}%
Mean Squared Error: {result.get('mse', 0):.1f}
        """
        
        tk.Label(
            details_frame,
            text=metrics_text,
            font=('Arial', 10),
            fg='white',
            bg='#4b4b4b',
            justify='left'
        ).pack(pady=5)
        
        # Show results and complete progress
        self.progress_var.set(100)
        self.results_frame.pack(fill="x", pady=20)
        self.reset_compare_button()
        
    def reset_compare_button(self):
        """Reset compare button to original state"""
        self.compare_btn.configure(state="normal", text="🔍 Compare Fingerprints")
        
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = SimpleFingerprintApp()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")
