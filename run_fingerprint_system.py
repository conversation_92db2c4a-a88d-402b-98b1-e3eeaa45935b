#!/usr/bin/env python3
"""
Universal Fingerprint System Launcher
Automatically tries different versions until one works
"""

import sys
import os
import subprocess

def check_dependencies():
    """Check basic dependencies"""
    required = ['cv2', 'numpy', 'PIL', 'customtkinter']
    missing = []
    
    for module in required:
        try:
            __import__(module)
        except ImportError:
            missing.append(module)
    
    return missing

def install_basic_deps():
    """Install basic dependencies"""
    packages = ['opencv-python', 'numpy', 'Pillow', 'customtkinter']
    
    for package in packages:
        try:
            print(f"Installing {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except:
            print(f"Failed to install {package}")

def run_working_system():
    """Run the working government system"""
    try:
        print("🏛️ Starting Working Government-Grade System...")
        from working_government_system import WorkingGovernmentSystem
        app = WorkingGovernmentSystem()
        app.run()
        return True
    except Exception as e:
        print(f"Working system failed: {e}")
        return False

def run_simple_system():
    """Run the simple system"""
    try:
        print("⚡ Starting Simple System...")
        from simple_fingerprint_gui import SimpleFingerprintApp
        app = SimpleFingerprintApp()
        app.run()
        return True
    except Exception as e:
        print(f"Simple system failed: {e}")
        return False

def run_original_government():
    """Try original government system"""
    try:
        print("🔬 Trying Original Government System...")
        from government_grade_gui import GovernmentGradeFingerprintApp
        app = GovernmentGradeFingerprintApp()
        app.run()
        return True
    except Exception as e:
        print(f"Original government system failed: {e}")
        return False

def main():
    """Main launcher with fallback options"""
    print("🔍 UNIVERSAL FINGERPRINT ANALYSIS SYSTEM")
    print("=" * 60)
    print("Automatically finding the best working version...")
    print()
    
    # Check dependencies
    missing = check_dependencies()
    if missing:
        print(f"Missing dependencies: {missing}")
        response = input("Install missing dependencies? (y/n): ").lower()
        if response == 'y':
            install_basic_deps()
        else:
            print("Cannot proceed without dependencies.")
            return
    
    # Try systems in order of preference
    systems = [
        ("Working Government System", run_working_system),
        ("Simple Enhanced System", run_simple_system),
        ("Original Government System", run_original_government),
    ]
    
    for name, run_func in systems:
        print(f"\n🚀 Trying {name}...")
        try:
            if run_func():
                print(f"✅ {name} started successfully!")
                return
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            continue
    
    print("\n❌ All systems failed. Please check:")
    print("1. Python installation")
    print("2. Required packages installed")
    print("3. Image file permissions")
    print("4. System compatibility")

if __name__ == "__main__":
    main()
