@echo off
echo 🔍 Fingerprint Comparison Tool
echo ============================
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)

echo.
echo Starting Fingerprint Comparison Tool...
echo.

REM Try to run the advanced version first
echo Attempting to run advanced version...
python main.py
if errorlevel 1 (
    echo.
    echo ⚠️ Advanced version failed, trying simple version...
    echo.
    python simple_fingerprint_gui.py
    if errorlevel 1 (
        echo.
        echo ❌ Both versions failed to start
        echo Please check the error messages above
        pause
        exit /b 1
    )
)

echo.
echo Application closed.
pause
