#!/usr/bin/env python3
"""
Complete Testing Guide for Government-Grade Fingerprint System
Step-by-step testing procedures and sample data generation
"""

import os
import sys
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
import random

def create_test_fingerprints():
    """Create realistic test fingerprint images for testing"""
    print("🧪 Creating Test Fingerprint Images...")
    print("=" * 50)
    
    # Create test directory
    test_dir = "test_fingerprints"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
        print(f"📁 Created directory: {test_dir}")
    
    # Test cases to create
    test_cases = [
        # Same person tests (should match)
        ("person1_finger1.jpg", "person1_finger2.jpg", "SAME", "Person 1 - Different captures"),
        ("person2_finger1.jpg", "person2_finger2.jpg", "SAME", "Person 2 - Different captures"),
        ("person3_finger1.jpg", "person3_finger2.jpg", "SAME", "Person 3 - Different captures"),
        
        # Different people tests (should NOT match)
        ("person1_finger1.jpg", "person2_finger1.jpg", "DIFFERENT", "Person 1 vs Person 2"),
        ("person2_finger1.jpg", "person3_finger1.jpg", "DIFFERENT", "Person 2 vs Person 3"),
        ("person1_finger2.jpg", "person3_finger2.jpg", "DIFFERENT", "Person 1 vs Person 3"),
        
        # Quality tests
        ("high_quality.jpg", "medium_quality.jpg", "QUALITY", "Quality comparison test"),
        ("good_print.jpg", "poor_print.jpg", "QUALITY", "Good vs Poor quality"),
    ]
    
    # Generate fingerprint-like patterns
    fingerprint_patterns = {}
    
    # Person 1 - Whorl pattern
    fingerprint_patterns["person1"] = create_whorl_pattern(seed=42)
    
    # Person 2 - Loop pattern  
    fingerprint_patterns["person2"] = create_loop_pattern(seed=123)
    
    # Person 3 - Arch pattern
    fingerprint_patterns["person3"] = create_arch_pattern(seed=456)
    
    # Create test images
    created_files = []
    
    for filename, _, _, description in test_cases:
        if filename not in created_files:
            # Determine person and quality
            if "person1" in filename:
                base_pattern = fingerprint_patterns["person1"]
                person = "person1"
            elif "person2" in filename:
                base_pattern = fingerprint_patterns["person2"] 
                person = "person2"
            elif "person3" in filename:
                base_pattern = fingerprint_patterns["person3"]
                person = "person3"
            elif "high_quality" in filename:
                base_pattern = fingerprint_patterns["person1"]
                person = "high_quality"
            elif "medium_quality" in filename:
                base_pattern = add_noise(fingerprint_patterns["person1"], 0.1)
                person = "medium_quality"
            elif "good_print" in filename:
                base_pattern = fingerprint_patterns["person2"]
                person = "good_print"
            elif "poor_print" in filename:
                base_pattern = add_noise(fingerprint_patterns["person2"], 0.3)
                person = "poor_print"
            else:
                base_pattern = fingerprint_patterns["person1"]
                person = "default"
            
            # Add variation for different captures of same person
            if "finger2" in filename:
                base_pattern = add_slight_variation(base_pattern)
            
            # Save image
            filepath = os.path.join(test_dir, filename)
            Image.fromarray(base_pattern).save(filepath)
            created_files.append(filename)
            print(f"✅ Created: {filename}")
    
    print(f"\n🎉 Created {len(created_files)} test images in '{test_dir}' directory")
    return test_cases

def create_whorl_pattern(seed=42, size=(400, 400)):
    """Create whorl fingerprint pattern"""
    np.random.seed(seed)
    img = np.zeros(size, dtype=np.uint8)
    center_x, center_y = size[1] // 2, size[0] // 2
    
    for y in range(size[0]):
        for x in range(size[1]):
            # Distance from center
            dx, dy = x - center_x, y - center_y
            r = np.sqrt(dx*dx + dy*dy)
            
            # Angle from center
            angle = np.arctan2(dy, dx)
            
            # Whorl pattern - circular ridges
            ridge_value = np.sin(r * 0.15 + angle * 3) * 0.5 + 0.5
            
            # Add some noise
            noise = np.random.normal(0, 0.1)
            value = (ridge_value + noise) * 255
            
            img[y, x] = np.clip(value, 0, 255)
    
    # Apply Gaussian blur for smoothing
    img_pil = Image.fromarray(img)
    img_pil = img_pil.filter(ImageFilter.GaussianBlur(radius=1))
    return np.array(img_pil)

def create_loop_pattern(seed=123, size=(400, 400)):
    """Create loop fingerprint pattern"""
    np.random.seed(seed)
    img = np.zeros(size, dtype=np.uint8)
    
    for y in range(size[0]):
        for x in range(size[1]):
            # Normalized coordinates
            nx, ny = (x - size[1]//2) / (size[1]//4), (y - size[0]//2) / (size[0]//4)
            
            # Loop pattern
            ridge_value = np.sin(nx * 2 + ny * 0.5) * np.cos(ny * 2) * 0.5 + 0.5
            
            # Add noise
            noise = np.random.normal(0, 0.1)
            value = (ridge_value + noise) * 255
            
            img[y, x] = np.clip(value, 0, 255)
    
    img_pil = Image.fromarray(img)
    img_pil = img_pil.filter(ImageFilter.GaussianBlur(radius=1))
    return np.array(img_pil)

def create_arch_pattern(seed=456, size=(400, 400)):
    """Create arch fingerprint pattern"""
    np.random.seed(seed)
    img = np.zeros(size, dtype=np.uint8)
    
    for y in range(size[0]):
        for x in range(size[1]):
            # Normalized coordinates
            nx, ny = (x - size[1]//2) / (size[1]//4), (y - size[0]//2) / (size[0]//4)
            
            # Arch pattern
            ridge_value = np.sin(nx * 1.5) * np.exp(-ny*ny * 0.5) * 0.5 + 0.5
            
            # Add noise
            noise = np.random.normal(0, 0.1)
            value = (ridge_value + noise) * 255
            
            img[y, x] = np.clip(value, 0, 255)
    
    img_pil = Image.fromarray(img)
    img_pil = img_pil.filter(ImageFilter.GaussianBlur(radius=1))
    return np.array(img_pil)

def add_noise(img, noise_level=0.1):
    """Add noise to simulate different capture conditions"""
    noisy_img = img.astype(np.float32)
    noise = np.random.normal(0, noise_level * 255, img.shape)
    noisy_img += noise
    return np.clip(noisy_img, 0, 255).astype(np.uint8)

def add_slight_variation(img, variation=0.05):
    """Add slight variation to simulate same finger different capture"""
    varied_img = img.astype(np.float32)
    
    # Add slight rotation effect
    variation_noise = np.random.normal(0, variation * 255, img.shape)
    varied_img += variation_noise
    
    return np.clip(varied_img, 0, 255).astype(np.uint8)

def run_automated_tests():
    """Run automated tests on the system"""
    print("\n🤖 Running Automated Tests...")
    print("=" * 50)
    
    try:
        # Test simple system first
        print("Testing Simple System...")
        from simple_fingerprint_gui import SimpleFingerprintApp
        
        # Create app instance
        app = SimpleFingerprintApp()
        
        # Test cases
        test_cases = [
            ("test_fingerprints/person1_finger1.jpg", "test_fingerprints/person1_finger2.jpg", True),
            ("test_fingerprints/person1_finger1.jpg", "test_fingerprints/person2_finger1.jpg", False),
        ]
        
        results = []
        for img1, img2, should_match in test_cases:
            if os.path.exists(img1) and os.path.exists(img2):
                app.image1_path = img1
                app.image2_path = img2
                result = app.simple_compare()
                similarity = result.get('similarity_percentage', 0)
                predicted_match = similarity >= 50
                
                correct = predicted_match == should_match
                results.append(correct)
                
                status = "✅ CORRECT" if correct else "❌ WRONG"
                expected = "MATCH" if should_match else "NO MATCH"
                predicted = "MATCH" if predicted_match else "NO MATCH"
                
                print(f"{status} | Expected: {expected} | Predicted: {predicted} | Score: {similarity:.1f}%")
        
        accuracy = (sum(results) / len(results)) * 100 if results else 0
        print(f"\n📊 Simple System Accuracy: {accuracy:.1f}%")
        
    except Exception as e:
        print(f"❌ Automated test failed: {e}")

def print_testing_instructions():
    """Print detailed testing instructions"""
    print("\n📋 MANUAL TESTING INSTRUCTIONS")
    print("=" * 60)
    
    print("""
🎯 STEP-BY-STEP TESTING GUIDE:

1️⃣ LAUNCH THE SYSTEM:
   python run_government_system.py
   
2️⃣ TEST SAME PERSON (Should Match):
   • Upload: test_fingerprints/person1_finger1.jpg
   • Upload: test_fingerprints/person1_finger2.jpg
   • Expected: IDENTIFICATION or PROBABLE_MATCH (70%+)

3️⃣ TEST DIFFERENT PEOPLE (Should NOT Match):
   • Upload: test_fingerprints/person1_finger1.jpg  
   • Upload: test_fingerprints/person2_finger1.jpg
   • Expected: EXCLUSION or INCONCLUSIVE (<50%)

4️⃣ TEST QUALITY ASSESSMENT:
   • Upload: test_fingerprints/high_quality.jpg
   • Check: Should show "EXCELLENT" or "GOOD" quality
   • Upload: test_fingerprints/poor_print.jpg  
   • Check: Should show "POOR" quality or be rejected

5️⃣ VERIFY STATISTICAL ANALYSIS:
   • Check Match Probability values
   • Check False Match Rate (FMR)
   • Check Confidence Levels
   • Check Minutiae counts

6️⃣ TEST EDGE CASES:
   • Very similar but different people
   • Poor quality images
   • Rotated or scaled images
   • Partial fingerprints

📊 EXPECTED RESULTS:
✅ Same person: 70%+ similarity, high confidence
❌ Different people: <50% similarity, low confidence  
🔍 Quality rejection: Poor images automatically rejected
📈 Statistical validation: Proper FMR and confidence values
    """)

def main():
    """Main testing function"""
    print("🧪 GOVERNMENT-GRADE FINGERPRINT SYSTEM TESTING")
    print("=" * 60)
    
    # Create test data
    test_cases = create_test_fingerprints()
    
    # Run automated tests
    run_automated_tests()
    
    # Print manual testing instructions
    print_testing_instructions()
    
    print("\n🚀 READY FOR TESTING!")
    print("Run: python run_government_system.py")
    print("Then follow the manual testing steps above.")

if __name__ == "__main__":
    main()
