import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import customtkinter as ctk
from PIL import Image, ImageTk
import threading
import os
from fingerprint_processor import FingerprintProcessor

class FingerprintComparisonApp:
    def __init__(self):
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("Fingerprint Comparison Tool")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 700)
        
        # Initialize processor
        self.processor = FingerprintProcessor()
        
        # Variables
        self.image1_path = None
        self.image2_path = None
        self.comparison_result = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main container
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text="🔍 Fingerprint Comparison Tool", 
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(pady=(20, 30))
        
        # Content frame
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Configure grid
        content_frame.grid_columnconfigure(0, weight=1)
        content_frame.grid_columnconfigure(1, weight=1)
        content_frame.grid_rowconfigure(1, weight=1)
        
        # Image upload sections
        self.setup_image_section(content_frame, "Fingerprint 1", 0, 0, "image1")
        self.setup_image_section(content_frame, "Fingerprint 2", 0, 1, "image2")
        
        # Comparison button
        self.compare_btn = ctk.CTkButton(
            content_frame,
            text="🔍 Compare Fingerprints",
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            command=self.compare_fingerprints
        )
        self.compare_btn.grid(row=2, column=0, columnspan=2, pady=20, padx=20, sticky="ew")
        
        # Progress bar
        self.progress_bar = ctk.CTkProgressBar(content_frame)
        self.progress_bar.grid(row=3, column=0, columnspan=2, pady=(0, 20), padx=20, sticky="ew")
        self.progress_bar.set(0)
        
        # Results section
        self.setup_results_section(content_frame)
        
    def setup_image_section(self, parent, title, row, col, image_type):
        """Setup image upload and display section"""
        # Frame for this image section
        img_frame = ctk.CTkFrame(parent)
        img_frame.grid(row=row, column=col, rowspan=2, padx=10, pady=10, sticky="nsew")
        img_frame.grid_rowconfigure(1, weight=1)
        
        # Title
        title_label = ctk.CTkLabel(img_frame, text=title, font=ctk.CTkFont(size=18, weight="bold"))
        title_label.grid(row=0, column=0, pady=(15, 10))
        
        # Image display area
        img_display = ctk.CTkLabel(
            img_frame, 
            text="📁 Click to upload image\n\nSupported formats:\nJPG, PNG, BMP",
            width=300,
            height=300,
            fg_color=("gray75", "gray25"),
            corner_radius=10
        )
        img_display.grid(row=1, column=0, padx=15, pady=(0, 10), sticky="nsew")
        
        # Upload button
        upload_btn = ctk.CTkButton(
            img_frame,
            text="📁 Upload Image",
            command=lambda: self.upload_image(image_type, img_display)
        )
        upload_btn.grid(row=2, column=0, pady=(0, 15))
        
        # Store references
        setattr(self, f"{image_type}_display", img_display)
        setattr(self, f"{image_type}_upload_btn", upload_btn)
        
    def setup_results_section(self, parent):
        """Setup results display section"""
        # Results frame
        self.results_frame = ctk.CTkFrame(parent)
        self.results_frame.grid(row=4, column=0, columnspan=2, padx=20, pady=(0, 20), sticky="ew")
        
        # Results title
        results_title = ctk.CTkLabel(
            self.results_frame, 
            text="📊 Comparison Results", 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        results_title.pack(pady=(15, 10))
        
        # Results content frame
        self.results_content = ctk.CTkFrame(self.results_frame)
        self.results_content.pack(fill="x", padx=15, pady=(0, 15))
        
        # Initially hidden
        self.results_frame.grid_remove()
        
    def upload_image(self, image_type, display_label):
        """Handle image upload"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("JPEG files", "*.jpg *.jpeg"),
            ("PNG files", "*.png"),
            ("All files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title=f"Select {image_type.replace('image', 'Fingerprint ')}",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # Store path
                setattr(self, f"{image_type}_path", file_path)
                
                # Load and display image
                img = Image.open(file_path)
                
                # Resize for display
                img.thumbnail((280, 280), Image.Resampling.LANCZOS)
                
                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(img)
                
                # Update display
                display_label.configure(image=photo, text="")
                display_label.image = photo  # Keep a reference
                
                # Update button text
                btn = getattr(self, f"{image_type}_upload_btn")
                btn.configure(text="✅ Image Loaded")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load image: {str(e)}")
                
    def compare_fingerprints(self):
        """Compare the two uploaded fingerprints"""
        if not self.image1_path or not self.image2_path:
            messagebox.showwarning("Warning", "Please upload both fingerprint images first!")
            return
            
        # Disable button and show progress
        self.compare_btn.configure(state="disabled", text="🔄 Comparing...")
        self.progress_bar.set(0)
        self.results_frame.grid_remove()
        
        # Run comparison in separate thread
        thread = threading.Thread(target=self.run_comparison)
        thread.daemon = True
        thread.start()
        
    def run_comparison(self):
        """Run fingerprint comparison in background thread"""
        try:
            # Update progress
            self.root.after(0, lambda: self.progress_bar.set(0.2))
            
            # Perform comparison
            result = self.processor.compare_fingerprints(self.image1_path, self.image2_path)
            
            # Update progress
            self.root.after(0, lambda: self.progress_bar.set(0.8))
            
            # Store result
            self.comparison_result = result
            
            # Update UI in main thread
            self.root.after(0, self.display_results)
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Comparison failed: {str(e)}"))
            self.root.after(0, self.reset_compare_button)
            
    def display_results(self):
        """Display comparison results"""
        if 'error' in self.comparison_result:
            messagebox.showerror("Error", self.comparison_result['error'])
            self.reset_compare_button()
            return
            
        # Clear previous results
        for widget in self.results_content.winfo_children():
            widget.destroy()
            
        result = self.comparison_result
        similarity = result['similarity_percentage']
        
        # Main similarity score
        similarity_frame = ctk.CTkFrame(self.results_content)
        similarity_frame.pack(fill="x", pady=10)
        
        # More strict thresholds to reduce false positives
        if similarity >= 85:
            color = "green"
            status = "✅ STRONG MATCH"
        elif similarity >= 70:
            color = "orange"
            status = "⚠️ POSSIBLE MATCH"
        elif similarity >= 50:
            color = "yellow"
            status = "🔍 WEAK SIMILARITY"
        else:
            color = "red"
            status = "❌ NO MATCH"
            
        # Similarity percentage
        similarity_label = ctk.CTkLabel(
            similarity_frame,
            text=f"{similarity:.1f}%",
            font=ctk.CTkFont(size=36, weight="bold"),
            text_color=color
        )
        similarity_label.pack(pady=10)
        
        # Status
        status_label = ctk.CTkLabel(
            similarity_frame,
            text=status,
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=color
        )
        status_label.pack(pady=(0, 10))
        
        # Detailed metrics
        details_frame = ctk.CTkFrame(self.results_content)
        details_frame.pack(fill="x", pady=(10, 0))
        
        details_title = ctk.CTkLabel(
            details_frame,
            text="📈 Detailed Analysis",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        details_title.pack(pady=(10, 5))
        
        # Metrics grid
        metrics_frame = ctk.CTkFrame(details_frame)
        metrics_frame.pack(fill="x", padx=15, pady=(0, 15))
        
        metrics = [
            ("Minutiae Similarity", f"{result['minutiae_score']:.1f}%"),
            ("SIFT Features Similarity", f"{result['sift_score']:.1f}%"),
            ("Minutiae Points (Image 1)", str(result['minutiae_count_1'])),
            ("Minutiae Points (Image 2)", str(result['minutiae_count_2'])),
            ("SIFT Features (Image 1)", str(result['sift_features_1'])),
            ("SIFT Features (Image 2)", str(result['sift_features_2']))
        ]
        
        for i, (label, value) in enumerate(metrics):
            row = i // 2
            col = i % 2
            
            metric_frame = ctk.CTkFrame(metrics_frame)
            metric_frame.grid(row=row, column=col, padx=5, pady=5, sticky="ew")
            
            ctk.CTkLabel(metric_frame, text=label, font=ctk.CTkFont(weight="bold")).pack(pady=(5, 0))
            ctk.CTkLabel(metric_frame, text=value).pack(pady=(0, 5))
            
        metrics_frame.grid_columnconfigure(0, weight=1)
        metrics_frame.grid_columnconfigure(1, weight=1)
        
        # Show results and complete progress
        self.progress_bar.set(1.0)
        self.results_frame.grid()
        self.reset_compare_button()
        
    def reset_compare_button(self):
        """Reset compare button to original state"""
        self.compare_btn.configure(state="normal", text="🔍 Compare Fingerprints")
        
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = FingerprintComparisonApp()
    app.run()
