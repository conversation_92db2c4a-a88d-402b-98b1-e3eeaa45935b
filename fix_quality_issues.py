#!/usr/bin/env python3
"""
Image Quality Enhancement Tool
Fix quality issues for government-grade fingerprint analysis
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import os

def enhance_fingerprint_image(input_path, output_path):
    """Enhance fingerprint image quality for government-grade analysis"""
    try:
        print(f"🔧 Enhancing: {input_path}")
        
        # Load image
        img = cv2.imread(input_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            print(f"❌ Cannot load image: {input_path}")
            return False
        
        # Step 1: Resize to proper dimensions (government standard)
        target_size = (512, 512)
        img = cv2.resize(img, target_size, interpolation=cv2.INTER_CUBIC)
        print(f"✅ Resized to {target_size}")
        
        # Step 2: Histogram equalization for better contrast
        img_eq = cv2.equalizeHist(img)
        print("✅ Applied histogram equalization")
        
        # Step 3: Noise reduction
        img_denoised = cv2.bilateralFilter(img_eq, 9, 75, 75)
        print("✅ Applied noise reduction")
        
        # Step 4: Enhance contrast using CLAHE
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        img_clahe = clahe.apply(img_denoised)
        print("✅ Applied CLAHE contrast enhancement")
        
        # Step 5: Sharpen the image
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        img_sharpened = cv2.filter2D(img_clahe, -1, kernel)
        print("✅ Applied sharpening")
        
        # Step 6: Final smoothing
        img_final = cv2.GaussianBlur(img_sharpened, (3, 3), 0)
        print("✅ Applied final smoothing")
        
        # Save enhanced image
        cv2.imwrite(output_path, img_final)
        print(f"💾 Saved enhanced image: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhancement failed: {e}")
        return False

def create_high_quality_samples():
    """Create high-quality sample fingerprint images"""
    print("🎨 Creating High-Quality Sample Images...")
    print("=" * 50)
    
    # Create samples directory
    samples_dir = "high_quality_samples"
    if not os.path.exists(samples_dir):
        os.makedirs(samples_dir)
        print(f"📁 Created directory: {samples_dir}")
    
    # Generate high-quality synthetic fingerprints
    samples = [
        ("sample_person1_thumb.jpg", create_quality_whorl_pattern()),
        ("sample_person1_index.jpg", create_quality_whorl_pattern(variation=0.1)),
        ("sample_person2_thumb.jpg", create_quality_loop_pattern()),
        ("sample_person2_index.jpg", create_quality_loop_pattern(variation=0.1)),
        ("sample_person3_thumb.jpg", create_quality_arch_pattern()),
        ("sample_person3_index.jpg", create_quality_arch_pattern(variation=0.1)),
    ]
    
    for filename, pattern in samples:
        filepath = os.path.join(samples_dir, filename)
        Image.fromarray(pattern).save(filepath, quality=95, dpi=(500, 500))
        print(f"✅ Created: {filename} (500 DPI)")
    
    print(f"\n🎉 Created {len(samples)} high-quality samples")
    return samples_dir

def create_quality_whorl_pattern(size=(512, 512), variation=0):
    """Create high-quality whorl pattern"""
    img = np.zeros(size, dtype=np.uint8)
    center_x, center_y = size[1] // 2, size[0] // 2
    
    for y in range(size[0]):
        for x in range(size[1]):
            dx, dy = x - center_x, y - center_y
            r = np.sqrt(dx*dx + dy*dy)
            angle = np.arctan2(dy, dx)
            
            # High-quality whorl with clear ridges
            ridge_freq = 0.12 + variation * 0.02
            ridge_value = np.sin(r * ridge_freq + angle * 4) * 0.6 + 0.4
            
            # Add fine details
            detail = np.sin(r * 0.3 + angle * 8) * 0.1
            ridge_value += detail
            
            # Smooth transitions
            if r > 180:
                fade = max(0, 1 - (r - 180) / 50)
                ridge_value = ridge_value * fade + 0.5 * (1 - fade)
            
            img[y, x] = np.clip(ridge_value * 255, 0, 255)
    
    # Apply smoothing for realistic appearance
    img_pil = Image.fromarray(img)
    img_pil = img_pil.filter(ImageFilter.GaussianBlur(radius=0.8))
    
    # Enhance contrast
    enhancer = ImageEnhance.Contrast(img_pil)
    img_pil = enhancer.enhance(1.3)
    
    return np.array(img_pil)

def create_quality_loop_pattern(size=(512, 512), variation=0):
    """Create high-quality loop pattern"""
    img = np.zeros(size, dtype=np.uint8)
    
    for y in range(size[0]):
        for x in range(size[1]):
            nx = (x - size[1]//2) / (size[1]//3)
            ny = (y - size[0]//2) / (size[0]//3)
            
            # High-quality loop with clear flow
            ridge_value = np.sin(nx * 2.5 + variation) * np.cos(ny * 2.2) * 0.5 + 0.5
            
            # Add loop curvature
            curve = np.sin(nx * 1.5 + ny * 0.8) * 0.2
            ridge_value += curve
            
            # Edge fading
            edge_dist = min(x, y, size[1]-x, size[0]-y)
            if edge_dist < 50:
                fade = edge_dist / 50
                ridge_value = ridge_value * fade + 0.5 * (1 - fade)
            
            img[y, x] = np.clip(ridge_value * 255, 0, 255)
    
    img_pil = Image.fromarray(img)
    img_pil = img_pil.filter(ImageFilter.GaussianBlur(radius=0.8))
    
    enhancer = ImageEnhance.Contrast(img_pil)
    img_pil = enhancer.enhance(1.2)
    
    return np.array(img_pil)

def create_quality_arch_pattern(size=(512, 512), variation=0):
    """Create high-quality arch pattern"""
    img = np.zeros(size, dtype=np.uint8)
    
    for y in range(size[0]):
        for x in range(size[1]):
            nx = (x - size[1]//2) / (size[1]//3)
            ny = (y - size[0]//2) / (size[0]//3)
            
            # High-quality arch with smooth flow
            ridge_value = np.sin(nx * 2 + variation) * np.exp(-ny*ny * 0.3) * 0.5 + 0.5
            
            # Add arch curvature
            arch_curve = np.cos(nx * 1.2) * np.exp(-abs(ny) * 0.5) * 0.2
            ridge_value += arch_curve
            
            # Edge fading
            edge_dist = min(x, y, size[1]-x, size[0]-y)
            if edge_dist < 40:
                fade = edge_dist / 40
                ridge_value = ridge_value * fade + 0.5 * (1 - fade)
            
            img[y, x] = np.clip(ridge_value * 255, 0, 255)
    
    img_pil = Image.fromarray(img)
    img_pil = img_pil.filter(ImageFilter.GaussianBlur(radius=0.7))
    
    enhancer = ImageEnhance.Contrast(img_pil)
    img_pil = enhancer.enhance(1.4)
    
    return np.array(img_pil)

def check_image_quality(image_path):
    """Check if image meets government-grade quality standards"""
    try:
        img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            return False, "Cannot load image"
        
        # Check resolution
        height, width = img.shape
        if width < 300 or height < 300:
            return False, f"Resolution too low: {width}x{height} (need 300x300 minimum)"
        
        # Check contrast
        contrast = np.std(img)
        if contrast < 30:
            return False, f"Low contrast: {contrast:.1f} (need 30+ for good quality)"
        
        # Check if image is too dark or too bright
        mean_brightness = np.mean(img)
        if mean_brightness < 50 or mean_brightness > 200:
            return False, f"Poor brightness: {mean_brightness:.1f} (need 50-200 range)"
        
        # Check for blur (using Laplacian variance)
        laplacian_var = cv2.Laplacian(img, cv2.CV_64F).var()
        if laplacian_var < 100:
            return False, f"Image too blurry: {laplacian_var:.1f} (need 100+ for sharpness)"
        
        return True, f"Good quality: Resolution {width}x{height}, Contrast {contrast:.1f}, Sharpness {laplacian_var:.1f}"
        
    except Exception as e:
        return False, f"Quality check failed: {e}"

def main():
    """Main quality enhancement function"""
    print("🔧 FINGERPRINT IMAGE QUALITY ENHANCEMENT TOOL")
    print("=" * 60)
    
    print("\n📋 Available Options:")
    print("1. Create high-quality sample images")
    print("2. Enhance existing images")
    print("3. Check image quality")
    print("4. All of the above")
    
    choice = input("\nSelect option (1-4): ").strip()
    
    if choice in ['1', '4']:
        # Create high-quality samples
        samples_dir = create_high_quality_samples()
        print(f"\n✅ High-quality samples created in '{samples_dir}' directory")
    
    if choice in ['2', '4']:
        # Enhance existing images
        print("\n🔧 Image Enhancement:")
        input_dir = input("Enter directory with images to enhance (or press Enter for current directory): ").strip()
        if not input_dir:
            input_dir = "."
        
        output_dir = "enhanced_images"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        enhanced_count = 0
        for filename in os.listdir(input_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                input_path = os.path.join(input_dir, filename)
                output_path = os.path.join(output_dir, f"enhanced_{filename}")
                
                if enhance_fingerprint_image(input_path, output_path):
                    enhanced_count += 1
        
        print(f"\n✅ Enhanced {enhanced_count} images in '{output_dir}' directory")
    
    if choice in ['3', '4']:
        # Check quality
        print("\n🔍 Quality Check:")
        check_dir = input("Enter directory to check (or press Enter for current directory): ").strip()
        if not check_dir:
            check_dir = "."
        
        for filename in os.listdir(check_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                filepath = os.path.join(check_dir, filename)
                is_good, message = check_image_quality(filepath)
                status = "✅" if is_good else "❌"
                print(f"{status} {filename}: {message}")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Use the enhanced/sample images in the government system")
    print("2. Run: python run_government_system.py")
    print("3. Upload the high-quality images for testing")
    
    print("\n💡 TIPS FOR REAL FINGERPRINTS:")
    print("• Use good lighting when taking photos")
    print("• Ensure finger is clean and dry")
    print("• Take photos at 500+ DPI if possible")
    print("• Avoid blur and shadows")
    print("• Ensure clear ridge patterns are visible")

if __name__ == "__main__":
    main()
