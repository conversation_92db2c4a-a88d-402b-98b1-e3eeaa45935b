#!/usr/bin/env python3
"""
Fingerprint Comparison Tool
A modern Python application for comparing fingerprint images with high accuracy.

Features:
- Modern dark-themed UI using CustomTkinter
- Advanced fingerprint processing with minutiae detection
- SIFT feature matching for enhanced accuracy
- Real-time progress indicators
- Detailed comparison results

Author: AI Assistant
Date: 2025
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from fingerprint_gui import FingerprintComparisonApp
    
    def main():
        """Main entry point for the application"""
        print("🔍 Starting Fingerprint Comparison Tool...")
        print("📋 Loading dependencies...")
        
        # Create and run the application
        app = FingerprintComparisonApp()
        print("✅ Application loaded successfully!")
        print("🚀 Launching GUI...")
        app.run()
        
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Error importing required modules: {e}")
    print("\n📦 Please install required dependencies:")
    print("pip install -r requirements.txt")
    print("\n🔧 Required packages:")
    print("- opencv-python")
    print("- numpy") 
    print("- Pillow")
    print("- scikit-image")
    print("- matplotlib")
    print("- customtkinter")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
