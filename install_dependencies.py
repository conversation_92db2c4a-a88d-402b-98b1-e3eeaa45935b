#!/usr/bin/env python3
"""
Dependency installer for Fingerprint Comparison Tool
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Install all required dependencies"""
    print("🔧 Installing dependencies for Fingerprint Comparison Tool...")
    print("=" * 60)
    
    # Required packages
    packages = [
        "opencv-python",
        "numpy", 
        "Pillow",
        "scikit-image",
        "matplotlib",
        "customtkinter"
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"📦 Installing {package}...")
        if install_package(package):
            print(f"✅ {package} installed successfully")
        else:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
        print()
    
    print("=" * 60)
    
    if failed_packages:
        print(f"⚠️ Some packages failed to install: {', '.join(failed_packages)}")
        print("\n🔧 You can try installing them manually:")
        for package in failed_packages:
            print(f"pip install {package}")
        print("\n📝 Note: The simple version (simple_fingerprint_gui.py) works with basic libraries")
    else:
        print("🎉 All dependencies installed successfully!")
        print("\n🚀 You can now run:")
        print("python main.py  (for advanced version)")
        print("python simple_fingerprint_gui.py  (for basic version)")

if __name__ == "__main__":
    main()
