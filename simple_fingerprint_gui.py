import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import threading
import os
import numpy as np

class SimpleFingerprintApp:
    def __init__(self):
        # Create main window
        self.root = tk.Tk()
        self.root.title("Fingerprint Comparison Tool")
        self.root.geometry("1000x700")
        self.root.configure(bg='#2b2b2b')
        
        # Variables
        self.image1_path = None
        self.image2_path = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 24, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Heading.TLabel', font=('Arial', 14, 'bold'), background='#2b2b2b', foreground='white')
        style.configure('Custom.TButton', font=('Arial', 12, 'bold'))
        
        # Main frame
        main_frame = tk.Frame(self.root, bg='#2b2b2b')
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(main_frame, text="🔍 Fingerprint Comparison Tool", style='Title.TLabel')
        title_label.pack(pady=(0, 30))
        
        # Content frame
        content_frame = tk.Frame(main_frame, bg='#2b2b2b')
        content_frame.pack(fill="both", expand=True)
        
        # Image sections
        images_frame = tk.Frame(content_frame, bg='#2b2b2b')
        images_frame.pack(fill="x", pady=20)
        
        # Image 1 section
        img1_frame = tk.Frame(images_frame, bg='#3b3b3b', relief='raised', bd=2)
        img1_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        ttk.Label(img1_frame, text="Fingerprint 1", style='Heading.TLabel').pack(pady=10)
        
        self.img1_display = tk.Label(
            img1_frame, 
            text="📁 Click to upload image\n\nSupported formats:\nJPG, PNG, BMP",
            width=40, height=15,
            bg='#4b4b4b', fg='white',
            font=('Arial', 10)
        )
        self.img1_display.pack(padx=15, pady=10)
        
        self.upload1_btn = ttk.Button(
            img1_frame, 
            text="📁 Upload Image 1",
            command=lambda: self.upload_image("image1", self.img1_display),
            style='Custom.TButton'
        )
        self.upload1_btn.pack(pady=(0, 15))
        
        # Image 2 section
        img2_frame = tk.Frame(images_frame, bg='#3b3b3b', relief='raised', bd=2)
        img2_frame.pack(side="right", fill="both", expand=True, padx=(10, 0))
        
        ttk.Label(img2_frame, text="Fingerprint 2", style='Heading.TLabel').pack(pady=10)
        
        self.img2_display = tk.Label(
            img2_frame, 
            text="📁 Click to upload image\n\nSupported formats:\nJPG, PNG, BMP",
            width=40, height=15,
            bg='#4b4b4b', fg='white',
            font=('Arial', 10)
        )
        self.img2_display.pack(padx=15, pady=10)
        
        self.upload2_btn = ttk.Button(
            img2_frame, 
            text="📁 Upload Image 2",
            command=lambda: self.upload_image("image2", self.img2_display),
            style='Custom.TButton'
        )
        self.upload2_btn.pack(pady=(0, 15))
        
        # Compare button
        self.compare_btn = ttk.Button(
            content_frame,
            text="🔍 Compare Fingerprints",
            command=self.compare_fingerprints,
            style='Custom.TButton'
        )
        self.compare_btn.pack(pady=20)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            content_frame, 
            variable=self.progress_var, 
            maximum=100
        )
        self.progress_bar.pack(fill="x", padx=50, pady=10)
        
        # Results frame
        self.results_frame = tk.Frame(content_frame, bg='#3b3b3b', relief='raised', bd=2)
        self.results_frame.pack(fill="x", pady=20)
        self.results_frame.pack_forget()  # Initially hidden
        
    def upload_image(self, image_type, display_label):
        """Handle image upload"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff"),
            ("All files", "*.*")
        ]
        
        file_path = filedialog.askopenfilename(
            title=f"Select {image_type.replace('image', 'Fingerprint ')}",
            filetypes=file_types
        )
        
        if file_path:
            try:
                # Store path
                setattr(self, f"{image_type}_path", file_path)
                
                # Load and display image
                img = Image.open(file_path)
                
                # Resize for display
                img.thumbnail((300, 200), Image.Resampling.LANCZOS)
                
                # Convert to PhotoImage
                photo = ImageTk.PhotoImage(img)
                
                # Update display
                display_label.configure(image=photo, text="")
                display_label.image = photo  # Keep a reference
                
                # Update button text
                if image_type == "image1":
                    self.upload1_btn.configure(text="✅ Image 1 Loaded")
                else:
                    self.upload2_btn.configure(text="✅ Image 2 Loaded")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load image: {str(e)}")
                
    def compare_fingerprints(self):
        """Compare the two uploaded fingerprints"""
        if not self.image1_path or not self.image2_path:
            messagebox.showwarning("Warning", "Please upload both fingerprint images first!")
            return
            
        # Disable button and show progress
        self.compare_btn.configure(state="disabled", text="🔄 Comparing...")
        self.progress_var.set(0)
        self.results_frame.pack_forget()
        
        # Run comparison in separate thread
        thread = threading.Thread(target=self.run_comparison)
        thread.daemon = True
        thread.start()
        
    def run_comparison(self):
        """Run fingerprint comparison in background thread"""
        try:
            # Simulate processing steps
            for i in range(1, 6):
                self.root.after(0, lambda p=i*20: self.progress_var.set(p))
                threading.Event().wait(0.5)  # Simulate processing time
            
            # Simple comparison based on image properties
            result = self.simple_compare()
            
            # Update UI in main thread
            self.root.after(0, lambda: self.display_results(result))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Comparison failed: {str(e)}"))
            self.root.after(0, self.reset_compare_button)
            
    def simple_compare(self):
        """Advanced fingerprint comparison with proper ridge analysis"""
        try:
            # Load images
            img1 = Image.open(self.image1_path).convert('L')
            img2 = Image.open(self.image2_path).convert('L')

            # Resize to standard size for comparison
            size = (300, 300)
            img1 = img1.resize(size, Image.Resampling.LANCZOS)
            img2 = img2.resize(size, Image.Resampling.LANCZOS)

            # Convert to numpy arrays
            arr1 = np.array(img1)
            arr2 = np.array(img2)

            # Enhanced preprocessing
            arr1 = self.enhance_fingerprint(arr1)
            arr2 = self.enhance_fingerprint(arr2)

            # Extract ridge patterns
            ridges1 = self.extract_ridge_patterns(arr1)
            ridges2 = self.extract_ridge_patterns(arr2)

            # Calculate multiple similarity metrics
            ridge_similarity = self.compare_ridge_patterns(ridges1, ridges2)
            texture_similarity = self.compare_texture_features(arr1, arr2)
            edge_similarity = self.compare_edge_features(arr1, arr2)
            histogram_similarity = self.compare_histograms(arr1, arr2)

            # Strict weighted combination - more conservative
            final_similarity = (
                ridge_similarity * 0.4 +      # Ridge patterns most important
                texture_similarity * 0.25 +   # Texture analysis
                edge_similarity * 0.25 +      # Edge detection
                histogram_similarity * 0.1    # Histogram comparison (least weight)
            )

            # Apply strict threshold - reduce false positives
            if final_similarity < 30:  # Very strict threshold
                final_similarity = 0
            elif final_similarity < 50:
                final_similarity *= 0.5  # Penalize low scores

            return {
                'similarity_percentage': final_similarity,
                'ridge_similarity': ridge_similarity,
                'texture_similarity': texture_similarity,
                'edge_similarity': edge_similarity,
                'histogram_similarity': histogram_similarity
            }

        except Exception as e:
            return {'error': str(e), 'similarity_percentage': 0}

    def enhance_fingerprint(self, img):
        """Enhanced fingerprint preprocessing"""
        # Histogram equalization
        hist_eq = np.zeros_like(img)
        for i in range(256):
            hist_eq[img == i] = i

        # Apply Gaussian blur to reduce noise
        from scipy import ndimage
        try:
            blurred = ndimage.gaussian_filter(img, sigma=0.8)
        except:
            # Fallback if scipy not available
            blurred = img

        # Enhance contrast using CLAHE-like approach
        enhanced = self.apply_clahe_simple(blurred)

        return enhanced

    def apply_clahe_simple(self, img):
        """Simple CLAHE implementation"""
        # Divide image into tiles
        tile_size = 50
        h, w = img.shape
        enhanced = np.copy(img)

        for i in range(0, h, tile_size):
            for j in range(0, w, tile_size):
                tile = img[i:min(i+tile_size, h), j:min(j+tile_size, w)]
                if tile.size > 0:
                    # Local histogram equalization
                    tile_min, tile_max = tile.min(), tile.max()
                    if tile_max > tile_min:
                        normalized = ((tile - tile_min) / (tile_max - tile_min) * 255).astype(np.uint8)
                        enhanced[i:min(i+tile_size, h), j:min(j+tile_size, w)] = normalized

        return enhanced

    def extract_ridge_patterns(self, img):
        """Extract ridge patterns using gradient analysis"""
        # Calculate gradients
        grad_x = np.gradient(img, axis=1)
        grad_y = np.gradient(img, axis=0)

        # Calculate gradient magnitude and direction
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        direction = np.arctan2(grad_y, grad_x)

        # Normalize direction to 0-180 degrees
        direction = (direction * 180 / np.pi) % 180

        # Create ridge pattern descriptor
        ridge_pattern = {
            'magnitude': magnitude,
            'direction': direction,
            'ridge_density': self.calculate_ridge_density(img),
            'orientation_map': self.calculate_orientation_map(direction)
        }

        return ridge_pattern

    def calculate_ridge_density(self, img):
        """Calculate ridge density in different regions"""
        h, w = img.shape
        block_size = 20
        density_map = np.zeros((h//block_size, w//block_size))

        for i in range(0, h, block_size):
            for j in range(0, w, block_size):
                block = img[i:min(i+block_size, h), j:min(j+block_size, w)]
                if block.size > 0:
                    # Count ridge-valley transitions
                    transitions = 0
                    for row in block:
                        for k in range(len(row)-1):
                            if abs(int(row[k]) - int(row[k+1])) > 30:  # Threshold for transition
                                transitions += 1
                    density_map[i//block_size, j//block_size] = transitions / block.size

        return density_map

    def calculate_orientation_map(self, direction):
        """Calculate orientation map"""
        h, w = direction.shape
        block_size = 15
        orientation_map = np.zeros((h//block_size, w//block_size))

        for i in range(0, h, block_size):
            for j in range(0, w, block_size):
                block = direction[i:min(i+block_size, h), j:min(j+block_size, w)]
                if block.size > 0:
                    # Calculate dominant orientation
                    orientation_map[i//block_size, j//block_size] = np.median(block)

        return orientation_map

    def compare_ridge_patterns(self, ridges1, ridges2):
        """Compare ridge patterns between two fingerprints"""
        try:
            # Compare ridge densities
            density_diff = np.mean(np.abs(ridges1['ridge_density'] - ridges2['ridge_density']))
            density_similarity = max(0, (1 - density_diff) * 100)

            # Compare orientation maps
            orientation_diff = np.mean(np.abs(ridges1['orientation_map'] - ridges2['orientation_map']))
            orientation_similarity = max(0, (1 - orientation_diff/180) * 100)

            # Compare gradient magnitudes
            mag1_norm = ridges1['magnitude'] / (np.max(ridges1['magnitude']) + 1e-8)
            mag2_norm = ridges2['magnitude'] / (np.max(ridges2['magnitude']) + 1e-8)
            magnitude_diff = np.mean(np.abs(mag1_norm - mag2_norm))
            magnitude_similarity = max(0, (1 - magnitude_diff) * 100)

            # Weighted combination
            ridge_similarity = (
                density_similarity * 0.4 +
                orientation_similarity * 0.4 +
                magnitude_similarity * 0.2
            )

            return ridge_similarity

        except Exception:
            return 0.0

    def compare_texture_features(self, img1, img2):
        """Compare texture features using Local Binary Patterns"""
        try:
            lbp1 = self.calculate_lbp(img1)
            lbp2 = self.calculate_lbp(img2)

            # Calculate histograms
            hist1, _ = np.histogram(lbp1.flatten(), bins=256, range=(0, 256))
            hist2, _ = np.histogram(lbp2.flatten(), bins=256, range=(0, 256))

            # Normalize histograms
            hist1 = hist1 / (np.sum(hist1) + 1e-8)
            hist2 = hist2 / (np.sum(hist2) + 1e-8)

            # Calculate chi-square distance
            chi_square = np.sum((hist1 - hist2)**2 / (hist1 + hist2 + 1e-8))
            similarity = max(0, (1 - chi_square/2) * 100)

            return similarity

        except Exception:
            return 0.0

    def calculate_lbp(self, img):
        """Calculate Local Binary Pattern"""
        h, w = img.shape
        lbp = np.zeros_like(img)

        for i in range(1, h-1):
            for j in range(1, w-1):
                center = img[i, j]
                pattern = 0

                # 8-neighborhood
                neighbors = [
                    img[i-1, j-1], img[i-1, j], img[i-1, j+1],
                    img[i, j+1], img[i+1, j+1], img[i+1, j],
                    img[i+1, j-1], img[i, j-1]
                ]

                for k, neighbor in enumerate(neighbors):
                    if neighbor >= center:
                        pattern |= (1 << k)

                lbp[i, j] = pattern

        return lbp

    def compare_edge_features(self, img1, img2):
        """Compare edge features"""
        try:
            # Simple edge detection using Sobel-like operator
            edges1 = self.detect_edges(img1)
            edges2 = self.detect_edges(img2)

            # Calculate edge density
            edge_density1 = np.sum(edges1 > 50) / edges1.size
            edge_density2 = np.sum(edges2 > 50) / edges2.size

            density_similarity = max(0, (1 - abs(edge_density1 - edge_density2)) * 100)

            # Calculate edge correlation
            correlation = np.corrcoef(edges1.flatten(), edges2.flatten())[0, 1]
            if np.isnan(correlation):
                correlation = 0

            edge_similarity = max(0, correlation * 100)

            # Combine metrics
            final_similarity = (density_similarity * 0.3 + edge_similarity * 0.7)

            return final_similarity

        except Exception:
            return 0.0

    def detect_edges(self, img):
        """Simple edge detection"""
        # Sobel operators
        sobel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]])
        sobel_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]])

        h, w = img.shape
        edges = np.zeros_like(img)

        for i in range(1, h-1):
            for j in range(1, w-1):
                region = img[i-1:i+2, j-1:j+2]
                gx = np.sum(region * sobel_x)
                gy = np.sum(region * sobel_y)
                edges[i, j] = np.sqrt(gx**2 + gy**2)

        return edges

    def compare_histograms(self, img1, img2):
        """Compare intensity histograms"""
        try:
            hist1, _ = np.histogram(img1.flatten(), bins=64, range=(0, 256))
            hist2, _ = np.histogram(img2.flatten(), bins=64, range=(0, 256))

            # Normalize
            hist1 = hist1 / (np.sum(hist1) + 1e-8)
            hist2 = hist2 / (np.sum(hist2) + 1e-8)

            # Calculate intersection
            intersection = np.sum(np.minimum(hist1, hist2))
            similarity = intersection * 100

            return similarity

        except Exception:
            return 0.0

    def display_results(self, result):
        """Display comparison results"""
        if 'error' in result:
            messagebox.showerror("Error", result['error'])
            self.reset_compare_button()
            return
            
        # Clear previous results
        for widget in self.results_frame.winfo_children():
            widget.destroy()
            
        similarity = result['similarity_percentage']
        
        # Results title
        ttk.Label(
            self.results_frame, 
            text="📊 Comparison Results", 
            style='Heading.TLabel'
        ).pack(pady=15)
        
        # Main similarity score
        similarity_frame = tk.Frame(self.results_frame, bg='#3b3b3b')
        similarity_frame.pack(pady=10)
        
        # More strict thresholds to reduce false positives
        if similarity >= 85:
            color = "green"
            status = "✅ STRONG MATCH"
        elif similarity >= 70:
            color = "orange"
            status = "⚠️ POSSIBLE MATCH"
        elif similarity >= 50:
            color = "yellow"
            status = "🔍 WEAK SIMILARITY"
        else:
            color = "red"
            status = "❌ NO MATCH"
            
        # Similarity percentage
        tk.Label(
            similarity_frame,
            text=f"{similarity:.1f}%",
            font=('Arial', 32, 'bold'),
            fg=color,
            bg='#3b3b3b'
        ).pack()
        
        # Status
        tk.Label(
            similarity_frame,
            text=status,
            font=('Arial', 16, 'bold'),
            fg=color,
            bg='#3b3b3b'
        ).pack()
        
        # Detailed metrics
        details_frame = tk.Frame(self.results_frame, bg='#4b4b4b')
        details_frame.pack(fill="x", padx=20, pady=10)
        
        tk.Label(
            details_frame,
            text="📈 Detailed Analysis",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='#4b4b4b'
        ).pack(pady=5)
        
        metrics_text = f"""
Ridge Pattern Similarity: {result.get('ridge_similarity', 0):.1f}%
Texture Feature Similarity: {result.get('texture_similarity', 0):.1f}%
Edge Feature Similarity: {result.get('edge_similarity', 0):.1f}%
Histogram Similarity: {result.get('histogram_similarity', 0):.1f}%

Analysis: {'Fingerprints appear to be from the same person' if similarity >= 70 else 'Fingerprints appear to be from different people' if similarity < 50 else 'Inconclusive - manual verification recommended'}
        """
        
        tk.Label(
            details_frame,
            text=metrics_text,
            font=('Arial', 10),
            fg='white',
            bg='#4b4b4b',
            justify='left'
        ).pack(pady=5)
        
        # Show results and complete progress
        self.progress_var.set(100)
        self.results_frame.pack(fill="x", pady=20)
        self.reset_compare_button()
        
    def reset_compare_button(self):
        """Reset compare button to original state"""
        self.compare_btn.configure(state="normal", text="🔍 Compare Fingerprints")
        
    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = SimpleFingerprintApp()
        app.run()
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")
