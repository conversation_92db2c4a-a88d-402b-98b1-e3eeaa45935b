#!/usr/bin/env python3
"""
Government-Grade Fingerprint Analysis System Launcher
FBI/NIST Standard Implementation
"""

import sys
import os
import subprocess

def check_dependencies():
    """Check if all government-grade dependencies are available"""
    print("🏛️ Government-Grade Fingerprint Analysis System")
    print("=" * 60)
    print("Checking FBI/NIST standard dependencies...")
    print()
    
    required_modules = {
        'cv2': 'opencv-python',
        'numpy': 'numpy', 
        'scipy': 'scipy',
        'skimage': 'scikit-image',
        'PIL': 'Pillow',
        'matplotlib': 'matplotlib',
        'customtkinter': 'customtkinter',
        'pandas': 'pandas'
    }
    
    missing_modules = []
    
    for module, package in required_modules.items():
        try:
            __import__(module)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_modules.append(package)
    
    if missing_modules:
        print(f"\n⚠️ Missing modules: {', '.join(missing_modules)}")
        print("\n📦 Install missing dependencies:")
        print("pip install -r government_requirements.txt")
        print("\nOr install individually:")
        for package in missing_modules:
            print(f"pip install {package}")
        return False
    else:
        print("\n🎉 All government-grade dependencies are available!")
        return True

def install_dependencies():
    """Install government-grade dependencies"""
    print("\n🔧 Installing government-grade dependencies...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "government_requirements.txt"
        ])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def run_government_system():
    """Run the government-grade fingerprint system"""
    try:
        print("\n🚀 Launching Government-Grade Fingerprint Analysis System...")
        print("📋 System Features:")
        print("   • FBI-standard minutiae extraction")
        print("   • NIST-compliant quality assessment")
        print("   • Bozorth3-style matching algorithm")
        print("   • Statistical validation & confidence intervals")
        print("   • Forensic-grade reporting")
        print("   • False match rate calculations")
        print()
        
        from government_grade_gui import GovernmentGradeFingerprintApp
        app = GovernmentGradeFingerprintApp()
        app.run()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n🔧 Try installing dependencies:")
        print("python run_government_system.py --install")
        return False
    except Exception as e:
        print(f"❌ Error starting system: {e}")
        return False

def run_simple_fallback():
    """Run simple version if government-grade fails"""
    try:
        print("\n🔄 Falling back to simple version...")
        from simple_fingerprint_gui import SimpleFingerprintApp
        app = SimpleFingerprintApp()
        app.run()
    except Exception as e:
        print(f"❌ Fallback also failed: {e}")

def main():
    """Main launcher function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--install":
        # Install dependencies
        if install_dependencies():
            print("\n✅ Installation complete! Run again without --install")
        sys.exit(0)
    
    # Check dependencies
    if not check_dependencies():
        response = input("\n❓ Install missing dependencies? (y/n): ").lower()
        if response == 'y':
            if install_dependencies():
                print("\n✅ Dependencies installed! Restarting system...")
            else:
                print("\n❌ Installation failed. Trying simple version...")
                run_simple_fallback()
                return
        else:
            print("\n🔄 Trying simple version...")
            run_simple_fallback()
            return
    
    # Run government-grade system
    if not run_government_system():
        print("\n🔄 Government system failed. Trying simple version...")
        run_simple_fallback()

if __name__ == "__main__":
    main()
